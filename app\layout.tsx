import type { Metadata } from "next";
import React from "react";
import "./globals.css";

export const metadata: Metadata = {
  title: {
    default: "<PERSON> - Computer Science Student & Developer",
    template: "%s | <PERSON>"
  },
  description: "Computer Science junior at Syracuse University, leading CuseHacks hackathon and building innovative web applications and Discord bots.",
  keywords: [
    "Alan Tom",
    "Computer Science",
    "Syracuse University",
    "CuseHacks",
    "Web Developer",
    "Discord Bot",
    "React",
    "Next.js",
    "Python"
  ],
  authors: [{ name: "<PERSON>", url: "https://alantom.dev" }],
  creator: "<PERSON>",
  openGraph: {
    title: "<PERSON> - Computer Science Student & Developer",
    description: "Computer Science junior at Syracuse University, leading CuseHacks hackathon and building innovative web applications and Discord bots.",
    url: "https://alantom.dev",
    siteName: "<PERSON> Portfolio",
    images: [
      {
        url: "/images/cockatiel.webp",
        width: 1200,
        height: 630,
        alt: "<PERSON>'s Portfolio - Computer Science Student and Developer",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "<PERSON> - Computer Science Student & Developer",
    description: "Computer Science junior at Syracuse University, leading CuseHacks hackathon and building innovative applications.",
    images: ["/images/cockatiel.webp"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/images/cockatiel.webp" type="image/webp" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="antialiased">
        <main role="main" id="main-content">
          {children}
        </main>
      </body>
    </html>
  );
}
